#!/usr/bin/env python3
"""
Smoke Tests for EcoCycle
Basic tests to verify core functionality works before and after refactoring.
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add project root to path
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT)

class SmokeTestCase(unittest.TestCase):
    """Basic smoke tests to verify core functionality."""

    def setUp(self):
        """Set up test environment."""
        # Create temporary directories for testing
        self.temp_dir = tempfile.mkdtemp()
        self.original_env = os.environ.copy()
        
        # Set test environment variables
        os.environ['TESTING'] = 'true'
        os.environ['LOG_DIR'] = os.path.join(self.temp_dir, 'logs')
        os.environ['DATA_DIR'] = os.path.join(self.temp_dir, 'data')
        
        # Create test directories
        os.makedirs(os.environ['LOG_DIR'], exist_ok=True)
        os.makedirs(os.environ['DATA_DIR'], exist_ok=True)

    def tearDown(self):
        """Clean up test environment."""
        # Restore original environment
        os.environ.clear()
        os.environ.update(self.original_env)
        
        # Clean up temporary directory
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_import_main_module(self):
        """Test that main module can be imported."""
        try:
            import main
            self.assertTrue(hasattr(main, 'main'))
        except ImportError as e:
            self.fail(f"Failed to import main module: {e}")

    def test_import_core_modules(self):
        """Test that core modules can be imported."""
        core_modules = [
            'core.database_manager',
            'core.error_handler',
            'core.cli_core',
            'core.argument_parser'
        ]
        
        for module_name in core_modules:
            with self.subTest(module=module_name):
                try:
                    __import__(module_name)
                except ImportError as e:
                    self.fail(f"Failed to import {module_name}: {e}")

    def test_import_config(self):
        """Test that config module can be imported."""
        try:
            import config.config as config
            # Check for essential config attributes
            self.assertTrue(hasattr(config, 'LOG_DIR'))
            self.assertTrue(hasattr(config, 'VERSION'))
        except ImportError as e:
            self.fail(f"Failed to import config: {e}")

    def test_import_utils(self):
        """Test that utility modules can be imported."""
        utils_modules = [
            'utils.app_functions',
            'utils.ascii_art'
        ]
        
        for module_name in utils_modules:
            with self.subTest(module=module_name):
                try:
                    __import__(module_name)
                except ImportError as e:
                    self.fail(f"Failed to import {module_name}: {e}")

    def test_import_apps(self):
        """Test that app modules can be imported."""
        app_modules = [
            'apps.menu',
            'apps.carbon_footprint',
            'apps.eco_tips'
        ]
        
        for module_name in app_modules:
            with self.subTest(module=module_name):
                try:
                    __import__(module_name)
                except ImportError as e:
                    self.fail(f"Failed to import {module_name}: {e}")

    @patch('sys.argv', ['main.py', '--version'])
    def test_cli_version_command(self):
        """Test that CLI version command works."""
        try:
            import cli
            # This should not raise an exception
            self.assertTrue(hasattr(cli, 'display_version'))
        except Exception as e:
            self.fail(f"CLI version command failed: {e}")

    def test_database_manager_basic(self):
        """Test basic database manager functionality."""
        try:
            import core.database_manager as db_manager
            
            # Test that we can create a database manager instance
            # without actually connecting to a database
            self.assertTrue(hasattr(db_manager, 'DatabaseManager'))
            
        except ImportError as e:
            self.fail(f"Failed to import database manager: {e}")

    def test_user_manager_basic(self):
        """Test basic user manager functionality."""
        try:
            from auth.user_management.user_manager import UserManager
            
            # Test that we can create a user manager instance
            # This is a basic smoke test - we're not testing full functionality
            self.assertTrue(UserManager is not None)
            
        except ImportError as e:
            self.fail(f"Failed to import user manager: {e}")

    def test_dependency_manager(self):
        """Test dependency manager functionality."""
        try:
            import core.dependency.dependency_manager as dep_manager
            
            # Test basic functionality
            self.assertTrue(hasattr(dep_manager, 'is_package_installed'))
            self.assertTrue(hasattr(dep_manager, 'ensure_packages'))
            
        except ImportError as e:
            self.fail(f"Failed to import dependency manager: {e}")

    def test_error_handler(self):
        """Test error handler functionality."""
        try:
            from core.error_handler import handle_error, DatabaseError
            
            # Test that error classes exist
            self.assertTrue(DatabaseError is not None)
            self.assertTrue(handle_error is not None)
            
        except ImportError as e:
            self.fail(f"Failed to import error handler: {e}")

    def test_project_structure_exists(self):
        """Test that expected project structure exists."""
        expected_dirs = [
            'core',
            'apps',
            'auth',
            'utils',
            'config',
            'models',
            'views',
            'controllers',
            'services'
        ]
        
        for dir_name in expected_dirs:
            dir_path = os.path.join(PROJECT_ROOT, dir_name)
            with self.subTest(directory=dir_name):
                self.assertTrue(os.path.exists(dir_path), 
                              f"Expected directory {dir_name} does not exist")

    def test_essential_files_exist(self):
        """Test that essential files exist."""
        essential_files = [
            'main.py',
            'cli.py',
            'pyproject.toml',
            'README.md',
            '__init__.py'
        ]
        
        for file_name in essential_files:
            file_path = os.path.join(PROJECT_ROOT, file_name)
            with self.subTest(file=file_name):
                self.assertTrue(os.path.exists(file_path), 
                              f"Essential file {file_name} does not exist")

class IntegrationSmokeTest(unittest.TestCase):
    """Integration smoke tests."""

    def setUp(self):
        """Set up integration test environment."""
        self.temp_dir = tempfile.mkdtemp()
        self.original_env = os.environ.copy()
        
        # Set test environment
        os.environ['TESTING'] = 'true'
        os.environ['LOG_DIR'] = os.path.join(self.temp_dir, 'logs')
        os.environ['DATA_DIR'] = os.path.join(self.temp_dir, 'data')
        os.makedirs(os.environ['LOG_DIR'], exist_ok=True)
        os.makedirs(os.environ['DATA_DIR'], exist_ok=True)

    def tearDown(self):
        """Clean up integration test environment."""
        os.environ.clear()
        os.environ.update(self.original_env)
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @patch('builtins.input', return_value='q')
    @patch('sys.stdout')
    def test_main_menu_loads(self, mock_stdout, mock_input):
        """Test that main menu can be loaded without errors."""
        try:
            from apps.menu import show_main_menu
            from auth.user_management.user_manager import UserManager
            
            # Create a mock user manager
            user_manager = MagicMock(spec=UserManager)
            user_manager.is_authenticated.return_value = False
            user_manager.is_guest.return_value = True
            
            # This should not raise an exception
            # We're just testing that the function can be called
            show_main_menu(user_manager)
            
        except Exception as e:
            self.fail(f"Main menu loading failed: {e}")

if __name__ == '__main__':
    # Create test directories if they don't exist
    test_dirs = ['tests/test_logs', 'tests/test_data']
    for test_dir in test_dirs:
        os.makedirs(test_dir, exist_ok=True)
    
    # Run the smoke tests
    unittest.main(verbosity=2)
