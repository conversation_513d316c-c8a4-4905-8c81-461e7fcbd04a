#!/usr/bin/env python3
"""
Safe Refactoring Script for EcoCycle
Automated script for safe file movement, import updates, and validation.
"""

import os
import sys
import shutil
import re
import json
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class SafeRefactor:
    """Safe refactoring tool with validation and rollback capabilities."""
    
    def __init__(self, dry_run: bool = True):
        self.dry_run = dry_run
        self.backup_dir = PROJECT_ROOT / "refactor_backup"
        self.changes_log = []
        self.setup_logging()
        
    def setup_logging(self):
        """Set up logging for refactoring operations."""
        log_file = PROJECT_ROOT / "logs" / "refactor.log"
        log_file.parent.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def create_backup(self) -> bool:
        """Create a complete backup of the current state."""
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
            
            self.backup_dir.mkdir(exist_ok=True)
            
            # Copy essential directories
            dirs_to_backup = [
                'core', 'apps', 'auth', 'utils', 'config', 
                'models', 'views', 'controllers', 'services',
                'Tests', 'Demo Modules'
            ]
            
            for dir_name in dirs_to_backup:
                src_dir = PROJECT_ROOT / dir_name
                if src_dir.exists():
                    dst_dir = self.backup_dir / dir_name
                    shutil.copytree(src_dir, dst_dir)
                    self.logger.info(f"Backed up {dir_name}")
            
            # Copy essential files
            files_to_backup = [
                'main.py', 'cli.py', 'mini.py', '__init__.py',
                'pyproject.toml', 'requirements.txt', 'setup.py'
            ]
            
            for file_name in files_to_backup:
                src_file = PROJECT_ROOT / file_name
                if src_file.exists():
                    dst_file = self.backup_dir / file_name
                    shutil.copy2(src_file, dst_file)
                    self.logger.info(f"Backed up {file_name}")
            
            self.logger.info(f"Backup created at {self.backup_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            return False
    
    def validate_imports(self, file_path: Path) -> List[str]:
        """Validate imports in a Python file."""
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract import statements
            import_pattern = r'^(?:from\s+[\w.]+\s+)?import\s+[\w.,\s*()]+$'
            imports = re.findall(import_pattern, content, re.MULTILINE)
            
            # Check each import
            for import_stmt in imports:
                # Skip relative imports and standard library
                if (import_stmt.startswith('from .') or 
                    import_stmt.startswith('import os') or
                    import_stmt.startswith('import sys')):
                    continue
                
                # Extract module name
                if 'from ' in import_stmt:
                    module = import_stmt.split('from ')[1].split(' import')[0].strip()
                else:
                    module = import_stmt.split('import ')[1].split('.')[0].strip()
                
                # Check if module exists in project
                module_path = PROJECT_ROOT / module.replace('.', '/')
                if not (module_path.exists() or (module_path.parent / f"{module_path.name}.py").exists()):
                    errors.append(f"Module not found: {module}")
        
        except Exception as e:
            errors.append(f"Error reading file: {e}")
        
        return errors
    
    def update_imports_in_file(self, file_path: Path, import_mappings: Dict[str, str]) -> bool:
        """Update imports in a single file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply import mappings
            for old_import, new_import in import_mappings.items():
                # Handle different import patterns
                patterns = [
                    (rf'^import {re.escape(old_import)}$', f'import {new_import}'),
                    (rf'^from {re.escape(old_import)} import', f'from {new_import} import'),
                    (rf'^import {re.escape(old_import)}\.', f'import {new_import}.'),
                ]
                
                for pattern, replacement in patterns:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            # Only write if content changed
            if content != original_content:
                if not self.dry_run:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                self.changes_log.append({
                    'type': 'import_update',
                    'file': str(file_path),
                    'mappings': import_mappings
                })
                self.logger.info(f"Updated imports in {file_path}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update imports in {file_path}: {e}")
            return False
    
    def move_file_safely(self, src: Path, dst: Path) -> bool:
        """Move a file safely with validation."""
        try:
            # Ensure destination directory exists
            dst.parent.mkdir(parents=True, exist_ok=True)
            
            # Check if destination already exists
            if dst.exists():
                self.logger.warning(f"Destination already exists: {dst}")
                return False
            
            if not self.dry_run:
                shutil.move(str(src), str(dst))
            
            self.changes_log.append({
                'type': 'file_move',
                'src': str(src),
                'dst': str(dst)
            })
            
            self.logger.info(f"Moved {src} -> {dst}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to move {src} to {dst}: {e}")
            return False
    
    def rename_directory(self, old_name: str, new_name: str) -> bool:
        """Rename a directory safely."""
        old_path = PROJECT_ROOT / old_name
        new_path = PROJECT_ROOT / new_name
        
        if not old_path.exists():
            self.logger.warning(f"Directory does not exist: {old_path}")
            return False
        
        if new_path.exists():
            self.logger.warning(f"Target directory already exists: {new_path}")
            return False
        
        try:
            if not self.dry_run:
                old_path.rename(new_path)
            
            self.changes_log.append({
                'type': 'directory_rename',
                'old': old_name,
                'new': new_name
            })
            
            self.logger.info(f"Renamed directory {old_name} -> {new_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to rename directory {old_name}: {e}")
            return False
    
    def run_tests(self) -> bool:
        """Run tests to validate changes."""
        try:
            # Run our smoke tests
            result = subprocess.run([
                sys.executable, 'tests/test_smoke.py'
            ], cwd=PROJECT_ROOT, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("All tests passed")
                return True
            else:
                self.logger.error(f"Tests failed: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to run tests: {e}")
            return False
    
    def rollback(self) -> bool:
        """Rollback all changes using backup."""
        try:
            if not self.backup_dir.exists():
                self.logger.error("No backup found for rollback")
                return False
            
            # Remove current directories
            dirs_to_restore = [
                'core', 'apps', 'auth', 'utils', 'config',
                'models', 'views', 'controllers', 'services'
            ]
            
            for dir_name in dirs_to_restore:
                current_dir = PROJECT_ROOT / dir_name
                backup_dir = self.backup_dir / dir_name
                
                if current_dir.exists():
                    shutil.rmtree(current_dir)
                
                if backup_dir.exists():
                    shutil.copytree(backup_dir, current_dir)
            
            # Restore files
            files_to_restore = [
                'main.py', 'cli.py', 'mini.py', '__init__.py',
                'pyproject.toml', 'requirements.txt', 'setup.py'
            ]
            
            for file_name in files_to_restore:
                current_file = PROJECT_ROOT / file_name
                backup_file = self.backup_dir / file_name
                
                if backup_file.exists():
                    shutil.copy2(backup_file, current_file)
            
            self.logger.info("Rollback completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False
    
    def save_changes_log(self):
        """Save the changes log for reference."""
        log_file = PROJECT_ROOT / "logs" / "refactor_changes.json"
        log_file.parent.mkdir(exist_ok=True)
        
        with open(log_file, 'w') as f:
            json.dump(self.changes_log, f, indent=2)
        
        self.logger.info(f"Changes log saved to {log_file}")

    def execute_phase1_reorganization(self) -> bool:
        """Execute Phase 1: Directory structure standardization."""
        self.logger.info("Starting Phase 1: Directory structure standardization")

        # Rename directories to follow Python conventions
        directory_renames = [
            ('Tests', 'tests'),
            ('Demo Modules', 'examples')
        ]

        success = True
        for old_name, new_name in directory_renames:
            if not self.rename_directory(old_name, new_name):
                success = False

        return success

    def execute_phase2_import_fixes(self) -> bool:
        """Execute Phase 2: Fix import statements."""
        self.logger.info("Starting Phase 2: Import statement fixes")

        # Define import mappings for common fixes
        import_mappings = {
            'Tests': 'tests',
            'Demo Modules': 'examples'
        }

        # Find all Python files
        python_files = list(PROJECT_ROOT.rglob('*.py'))

        success = True
        for file_path in python_files:
            # Skip backup directory and __pycache__
            if 'refactor_backup' in str(file_path) or '__pycache__' in str(file_path):
                continue

            if not self.update_imports_in_file(file_path, import_mappings):
                success = False

        return success

def main():
    """Main function for testing the refactor script."""
    import argparse

    parser = argparse.ArgumentParser(description='Safe refactoring tool for EcoCycle')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Run in dry-run mode (default)')
    parser.add_argument('--execute', action='store_true',
                       help='Execute changes (overrides dry-run)')
    parser.add_argument('--backup', action='store_true',
                       help='Create backup only')
    parser.add_argument('--rollback', action='store_true',
                       help='Rollback to backup')
    parser.add_argument('--phase', type=int, choices=[1, 2],
                       help='Execute specific phase')

    args = parser.parse_args()

    # Execute mode overrides dry-run
    dry_run = not args.execute if args.execute else args.dry_run

    refactor = SafeRefactor(dry_run=dry_run)

    if args.rollback:
        success = refactor.rollback()
        sys.exit(0 if success else 1)

    if args.backup:
        success = refactor.create_backup()
        sys.exit(0 if success else 1)

    # Example usage - create backup and validate
    print(f"Running in {'DRY-RUN' if dry_run else 'EXECUTE'} mode")

    if not refactor.create_backup():
        print("Failed to create backup")
        sys.exit(1)

    print("Backup created successfully")

    # Execute specific phase or all phases
    if args.phase == 1:
        success = refactor.execute_phase1_reorganization()
    elif args.phase == 2:
        success = refactor.execute_phase2_import_fixes()
    else:
        # Run validation tests first
        if not refactor.run_tests():
            print("Current state validation failed")
            sys.exit(1)
        print("Current state validation passed")
        success = True

    if success:
        print("Phase completed successfully")
        # Run tests after changes
        if not refactor.run_tests():
            print("Tests failed after changes - consider rollback")
            sys.exit(1)
        print("All tests passed after changes")
    else:
        print("Phase failed")
        sys.exit(1)

    refactor.save_changes_log()

if __name__ == '__main__':
    main()
