#!/usr/bin/env python3
"""
EcoCycle Reorganization Plan
Comprehensive plan for safe code reorganization following Python best practices.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import json

# Add project root to path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

class ReorganizationPlan:
    """Comprehensive reorganization plan for EcoCycle."""
    
    def __init__(self):
        self.plan = {
            'phases': [],
            'file_moves': {},
            'directory_renames': {},
            'import_mappings': {},
            'validation_steps': []
        }
        self.create_plan()
    
    def create_plan(self):
        """Create the comprehensive reorganization plan."""
        
        # Phase 1: Directory Structure Standardization
        self.plan['phases'].append({
            'name': 'Phase 1: Directory Structure Standardization',
            'description': 'Rename directories to follow Python conventions',
            'actions': [
                'Rename Tests/ to tests/',
                'Rename "Demo Modules/" to examples/',
                'Create logs/ directory if not exists',
                'Ensure all directory names use lowercase with underscores'
            ],
            'validation': [
                'Check that all directories follow naming conventions',
                'Verify no broken directory references',
                'Run basic import tests'
            ]
        })
        
        # Directory renames for Phase 1
        self.plan['directory_renames'] = {
            'Tests': 'tests',
            'Demo Modules': 'examples'
        }
        
        # Phase 2: File Organization and Cleanup
        self.plan['phases'].append({
            'name': 'Phase 2: File Organization and Cleanup',
            'description': 'Organize files into proper package structure',
            'actions': [
                'Move scattered configuration files to config/',
                'Organize utility scripts in scripts/',
                'Clean up root directory',
                'Remove duplicate and backup files'
            ],
            'validation': [
                'Verify all files are in correct locations',
                'Check that no essential files were lost',
                'Validate package structure'
            ]
        })
        
        # File moves for Phase 2
        self.plan['file_moves'] = {
            # Move any scattered config files
            'template.env': 'config/template.env',
            # Clean up any backup files that shouldn't be in root
            # These will be identified dynamically
        }
        
        # Phase 3: Import Statement Updates
        self.plan['phases'].append({
            'name': 'Phase 3: Import Statement Updates',
            'description': 'Update all import statements to reflect new structure',
            'actions': [
                'Update imports from Tests to tests',
                'Update imports from "Demo Modules" to examples',
                'Fix any broken relative imports',
                'Standardize import patterns'
            ],
            'validation': [
                'Run import validation on all Python files',
                'Check for circular imports',
                'Verify all modules can be imported'
            ]
        })
        
        # Import mappings for Phase 3
        self.plan['import_mappings'] = {
            'Tests': 'tests',
            'Demo Modules': 'examples',
            'tests.test_': 'tests.test_',  # Ensure test imports work
        }
        
        # Phase 4: Package Structure Optimization
        self.plan['phases'].append({
            'name': 'Phase 4: Package Structure Optimization',
            'description': 'Optimize package structure and __init__.py files',
            'actions': [
                'Review and update __init__.py files',
                'Ensure proper package exports',
                'Optimize import paths',
                'Create package documentation'
            ],
            'validation': [
                'Test package imports',
                'Verify public API accessibility',
                'Check package documentation'
            ]
        })
        
        # Phase 5: Configuration and Documentation Updates
        self.plan['phases'].append({
            'name': 'Phase 5: Configuration and Documentation Updates',
            'description': 'Update configuration files and documentation',
            'actions': [
                'Update pyproject.toml package references',
                'Update setup.py if needed',
                'Update README.md with new structure',
                'Update any documentation references'
            ],
            'validation': [
                'Verify package can be built',
                'Check that entry points work',
                'Validate documentation accuracy'
            ]
        })
        
        # Phase 6: Final Testing and Validation
        self.plan['phases'].append({
            'name': 'Phase 6: Final Testing and Validation',
            'description': 'Comprehensive testing and validation',
            'actions': [
                'Run full test suite',
                'Test all entry points',
                'Validate application functionality',
                'Performance testing'
            ],
            'validation': [
                'All tests pass',
                'Application runs without errors',
                'No performance regressions',
                'All features work as expected'
            ]
        })
        
        # Overall validation steps
        self.plan['validation_steps'] = [
            'Create backup before each phase',
            'Run smoke tests after each phase',
            'Validate imports after structural changes',
            'Test application functionality',
            'Check for any broken references',
            'Verify package can be installed',
            'Run comprehensive test suite'
        ]
    
    def get_files_to_move(self) -> Dict[str, str]:
        """Get dynamic list of files that need to be moved."""
        files_to_move = self.plan['file_moves'].copy()
        
        # Find backup files and temporary files that should be cleaned up
        for file_path in PROJECT_ROOT.rglob('*'):
            if file_path.is_file():
                # Skip hidden files and directories
                if any(part.startswith('.') for part in file_path.parts):
                    continue
                
                # Identify files that should be moved
                if file_path.suffix == '.bak':
                    # Move backup files to backups directory
                    rel_path = file_path.relative_to(PROJECT_ROOT)
                    files_to_move[str(rel_path)] = f'backups/{rel_path}'
                
                elif file_path.name.endswith('_backup.py'):
                    # Move backup Python files
                    rel_path = file_path.relative_to(PROJECT_ROOT)
                    files_to_move[str(rel_path)] = f'backups/{rel_path}'
        
        return files_to_move
    
    def get_directories_to_create(self) -> List[str]:
        """Get list of directories that need to be created."""
        return [
            'logs',
            'backups',
            'temp',
            'tests/test_data',
            'tests/test_logs',
            'examples/data',
            'config/templates'
        ]
    
    def get_files_to_remove(self) -> List[str]:
        """Get list of files that should be removed."""
        files_to_remove = []
        
        # Find duplicate files
        for file_path in PROJECT_ROOT.rglob('*'):
            if file_path.is_file():
                # Remove temporary files
                if file_path.suffix in ['.tmp', '.temp']:
                    files_to_remove.append(str(file_path.relative_to(PROJECT_ROOT)))
                
                # Remove old backup files (but keep recent ones)
                if '_old' in file_path.name or '_backup_' in file_path.name:
                    files_to_remove.append(str(file_path.relative_to(PROJECT_ROOT)))
        
        return files_to_remove
    
    def save_plan(self, output_file: str = None):
        """Save the reorganization plan to a JSON file."""
        if output_file is None:
            output_file = PROJECT_ROOT / 'logs' / 'reorganization_plan.json'
        
        # Ensure logs directory exists
        Path(output_file).parent.mkdir(exist_ok=True)
        
        # Add dynamic file information
        plan_with_details = self.plan.copy()
        plan_with_details['dynamic_file_moves'] = self.get_files_to_move()
        plan_with_details['directories_to_create'] = self.get_directories_to_create()
        plan_with_details['files_to_remove'] = self.get_files_to_remove()
        
        with open(output_file, 'w') as f:
            json.dump(plan_with_details, f, indent=2)
        
        print(f"Reorganization plan saved to: {output_file}")
    
    def print_summary(self):
        """Print a summary of the reorganization plan."""
        print("="*80)
        print("ECOCYCLE REORGANIZATION PLAN SUMMARY")
        print("="*80)
        
        print(f"\nTotal Phases: {len(self.plan['phases'])}")
        
        for i, phase in enumerate(self.plan['phases'], 1):
            print(f"\n{i}. {phase['name']}")
            print(f"   Description: {phase['description']}")
            print(f"   Actions: {len(phase['actions'])} planned")
            print(f"   Validations: {len(phase['validation'])} checks")
        
        print(f"\nDirectory Renames: {len(self.plan['directory_renames'])}")
        for old, new in self.plan['directory_renames'].items():
            print(f"  {old} -> {new}")
        
        print(f"\nStatic File Moves: {len(self.plan['file_moves'])}")
        dynamic_moves = self.get_files_to_move()
        print(f"Dynamic File Moves: {len(dynamic_moves)}")
        
        print(f"\nDirectories to Create: {len(self.get_directories_to_create())}")
        print(f"Files to Remove: {len(self.get_files_to_remove())}")
        
        print(f"\nValidation Steps: {len(self.plan['validation_steps'])}")
        
        print("\n" + "="*80)

def main():
    """Main function to create and display the reorganization plan."""
    plan = ReorganizationPlan()
    
    # Print summary
    plan.print_summary()
    
    # Save detailed plan
    plan.save_plan()
    
    print("\nReorganization plan created successfully!")
    print("Use 'python scripts/safe_refactor.py --phase N' to execute individual phases")
    print("Use 'python scripts/safe_refactor.py --backup' to create a backup first")

if __name__ == '__main__':
    main()
